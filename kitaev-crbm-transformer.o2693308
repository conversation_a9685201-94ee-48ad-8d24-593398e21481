Job start at: Wed Sep  3 16:33:19 +08 2025
Running on node: hpc-pinaki-gpu2
GPU Information:
Wed Sep  3 16:33:19 2025       
+-----------------------------------------------------------------------------------------+
| NVIDIA-SMI 580.65.06              Driver Version: 580.65.06      CUDA Version: 13.0     |
+-----------------------------------------+------------------------+----------------------+
| GPU  Name                 Persistence-M | Bus-Id          Disp.A | Volatile Uncorr. ECC |
| Fan  Temp   Perf          Pwr:Usage/Cap |           Memory-Usage | GPU-Util  Compute M. |
|                                         |                        |               MIG M. |
|=========================================+========================+======================|
|   0  NVIDIA H200 NVL                On  |   00000001:B0:00.0 Off |                    0 |
| N/A   37C    P0             72W /  600W |       0MiB / 143771MiB |      0%      Default |
|                                         |                        |             Disabled |
+-----------------------------------------+------------------------+----------------------+

+-----------------------------------------------------------------------------------------+
| Processes:                                                                              |
|  GPU   GI   CI              PID   Type   Process name                        GPU Memory |
|        ID   ID                                                               Usage      |
|=========================================================================================|
|  No running processes found                                                             |
+-----------------------------------------------------------------------------------------+
Loading modules...
Anaconda 2025 python3 module loaded.

Please run the following commands (include the quote): eval "$(/usr/local/anaconda2025/bin/conda shell.bash hook)"

Loading anaconda2025/2025
  Loading requirement: cuda/12.2
Using GPU device: 0
Python path: /home/<USER>/.conda/envs/netket/bin/python
Python version: Python 3.12.11
Current conda environment: netket
==================== cRBM-Transformer 混合架构训练 ====================
Starting cRBM-Transformer hybrid architecture training...
Checkpoint enabled with interval: 500
Using model: cRBM_Transformer
Experiment name: crbm_transformer_train
Traceback (most recent call last):
  File "<frozen runpy>", line 198, in _run_module_as_main
  File "<frozen runpy>", line 88, in _run_code
  File "/home/<USER>/Repositories/Kitaev_model/src/train.py", line 14, in <module>
    import hydra
ModuleNotFoundError: No module named 'hydra'
cRBM-Transformer训练完成！混合架构模型已保存。
Job finished at: Wed Sep  3 16:33:24 +08 2025
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
			Resource Usage on 2025-09-03 16:33:24.431505:
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	JobId: 2693308.hpc-pbs-sched1
	Project: gs_spms_psengupta
	Exit Status: 0
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	NCPUs: Requested(4), Used(4)
	CPU Time Used: 00:00:00
	Memory: Requested(75gb), Used(0b)
	Vmem Used: 0b
	Walltime: Requested(1440:00:00), Used(00:00:10)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	Execution Nodes Used: (hpc-pinaki-gpu2:ngpus=1:ncpus=4:mem=78643200kb)
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~
	No GPU-related information available for this job.
