import time
import math
import jax
import jax.numpy as jnp
from jax import tree_util
from netket.experimental.driver.vmc_srt import VMC_SRt
from src.utils.logging import log_message
    
# 定义熵梯度计算函数
def T_logp2(params, inputs, temperature, model_instance):
    variables = {"params": params}
    preds = model_instance.apply(variables, inputs)
    return 2.0 * temperature * jnp.mean(jnp.real(preds)**2)

def T_logp_2(params, inputs, temperature, model_instance):
    variables = {"params": params}
    preds = model_instance.apply(variables, inputs)
    return 2.0 * temperature * (jnp.mean(jnp.real(preds)))**2

# 基于 VMC_SRt 实现自由能 F = E - T*S 的优化
def clip_gradients(grad, max_norm):
    # 计算所有梯度的L2范数
    total_norm = jnp.sqrt(sum([jnp.sum(jnp.square(x)) for x in jax.tree_leaves(grad)]))
    # 如果总范数超过max_norm，则计算缩放因子，否则为1.0
    clip_coef = jnp.where(total_norm > max_norm, max_norm / (total_norm + 1e-6), 1.0)
    # 对所有梯度乘以缩放因子
    return jax.tree_map(lambda x: x * clip_coef, grad)

# 在你的训练步长中，例如在 _step_with_state 中修改梯度更新部分：
class FreeEnergyVMC_SRt(VMC_SRt):
    def __init__(self, temperature, clip_norm=1.0, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.init_temperature = temperature
        self.temperature = temperature
        self.clip_norm = clip_norm  # 设置梯度裁剪的阈值

    def _step_with_state(self, state):
        new_state = super()._step_with_state(state)
        params = new_state.parameters
        inputs = new_state.samples

        # 计算熵梯度部分（同之前代码）
        mT_grad_S_1 = jax.grad(T_logp2, argnums=0)(params, inputs, self.temperature, self.variational_state.model)
        mT_grad_S_2 = jax.grad(T_logp_2, argnums=0)(params, inputs, self.temperature, self.variational_state.model)
        mT_grad_S = tree_util.tree_map(lambda x, y: x - y, mT_grad_S_1, mT_grad_S_2)
        
        total_grad = tree_util.tree_map(lambda g_e, g_s: g_e - g_s,
                                          new_state.gradient, mT_grad_S)
        
        # 对梯度进行裁剪
        total_grad = clip_gradients(total_grad, self.clip_norm)

        new_params = self.optimizer.update(total_grad, params)
        new_state = new_state.replace(parameters=new_params)
        return new_state

# 添加进度条以及温度递减方案
class CustomFreeEnergyVMC_SRt(FreeEnergyVMC_SRt):
    def __init__(self, reference_energy=None, initial_period=100, period_mult=2.0, 
                 max_temperature=1.0, min_temperature=0.01, 
                 checkpoint_callback=None, checkpoint_interval=500, *args, **kwargs):
        # 设置初始温度为最大温度
        kwargs['temperature'] = max_temperature
        super().__init__(*args, **kwargs)
        self.reference_energy = reference_energy
        self.max_nan_count = 5  # 默认最大允许连续NaN次数
        
        # 热重启参数
        self.initial_period = initial_period      # 初始周期长度
        self.period_mult = period_mult            # 周期倍数
        self.max_temperature = max_temperature    # 最大温度
        self.min_temperature = min_temperature    # 最小温度
        
        # 记录重启信息
        self.current_restart = 0
        self.current_period = initial_period
        self.iter_since_restart = 0
        
        # Checkpoint相关
        self.checkpoint_callback = checkpoint_callback
        self.checkpoint_interval = checkpoint_interval

    def _cosine_annealing_with_restart(self, iteration):
        """
        带热重启的余弦退火温度调度
        T(i) = T_min + (T_max - T_min) * (1 + cos(π * i_cur / T_cur)) / 2
        
        其中:
        - T_max: 最大温度
        - T_min: 最小温度
        - i_cur: 当前重启周期内的迭代次数
        - T_cur: 当前重启周期长度
        """
        
        # 检查是否需要重启
        if self.iter_since_restart >= self.current_period:
            self.current_restart += 1
            self.iter_since_restart = 0
            # 更新下一个重启周期长度
            self.current_period = int(self.initial_period * (self.period_mult ** self.current_restart))
            
        cosine_factor = (1 + math.cos(math.pi * self.iter_since_restart / self.current_period)) / 2
        temperature = self.min_temperature + (self.max_temperature - self.min_temperature) * cosine_factor
        
        self.iter_since_restart += 1
        return temperature
    


    def run(self, n_iter, energy_log):
        """
        运行优化，通过 log_message 打印 Temperature, Learning Rate, Energy, 
        E_var, E_err，以及当提供 reference_energy 时显示的 Rel_err(%)
        
        添加了NaN检测机制：连续5次Energy为NaN时自动停止训练
        使用带热重启的余弦退火温度调度
        """
        nan_count = 0  # 连续NaN计数器
        

        
        for i in range(n_iter):
            # 使用带热重启的余弦退火更新温度
            prev_temperature = self.temperature
            self.temperature = self._cosine_annealing_with_restart(i)
            
            # 检测重启（简化日志）
            if self.temperature > prev_temperature * 1.5:
                log_message(energy_log, f"RESTART #{self.current_restart} | Period: {self.current_period}")
            
            self.advance(1)

            energy_mean = self.energy.mean
            energy_var = self.energy.variance
            energy_error = self.energy.error_of_mean

            # 检查Energy是否为NaN
            if jnp.isnan(energy_mean):
                nan_count += 1
                log_message(energy_log, f"⚠️  NaN Energy at iter {i+1}/{n_iter} ({nan_count}/{self.max_nan_count})")
                
                # 如果连续NaN次数达到阈值，停止训练
                if nan_count >= self.max_nan_count:
                    log_message(energy_log, f"❌ Stopping: {self.max_nan_count} consecutive NaN values")
                    break
            else:
                # Energy正常，重置NaN计数器
                nan_count = 0
                
                # 正常的能量日志记录
                restart_info = f"R{self.current_restart}[{self.iter_since_restart-1}/{self.current_period}]"
                
                if self.reference_energy is not None:
                    relative_error = abs((energy_mean - self.reference_energy) / self.reference_energy) * 100
                    log_message(
                        energy_log,
                        f"[Iter {i+1}/{n_iter}] {restart_info}, Temp: {self.temperature:.4f}, "
                        f"Energy: {energy_mean:.6f}, Rel_err(%): {relative_error:.4f}"
                    )
                else:
                    log_message(
                        energy_log,
                        f"[Iter {i+1}/{n_iter}] {restart_info}, Temp: {self.temperature:.4f}, "
                        f"Energy: {energy_mean:.6f}"
                    )
                
                # 保存checkpoint（如果启用且到达间隔）
                if (self.checkpoint_callback is not None and 
                    self.checkpoint_interval > 0 and 
                    (i + 1) % self.checkpoint_interval == 0):
                    self.checkpoint_callback(i + 1, energy_mean, energy_error)
        
        # 训练结束总结
        if nan_count >= self.max_nan_count:
            log_message(energy_log, "❌ TRAINING TERMINATED: Persistent NaN values")
        else:
            log_message(energy_log, f"✅ Training completed | Restarts: {self.current_restart}")
        
        return self 